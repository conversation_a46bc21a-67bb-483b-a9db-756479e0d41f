// Character utilities and operations for Fluent Lang

struct Char {
    value: string; // Single character string
}

enum CharType {
    Letter,
    Digit,
    Whitespace,
    Punctuation,
    Symbol,
    Control
}

// Character constants
struct CharConstants {
    // Static constants
}

CharConstants {
    static fun space(): string { return " "; }
    static fun tab(): string { return "\t"; }
    static fun newline(): string { return "\n"; }
    static fun carriage_return(): string { return "\r"; }
    static fun null_char(): string { return "\0"; }
    
    // ASCII ranges
    static fun ascii_zero(): num { return 48; }  // '0'
    static fun ascii_nine(): num { return 57; }  // '9'
    static fun ascii_upper_a(): num { return 65; }  // 'A'
    static fun ascii_upper_z(): num { return 90; }  // 'Z'
    static fun ascii_lower_a(): num { return 97; }  // 'a'
    static fun ascii_lower_z(): num { return 122; } // 'z'
}

// Character utility functions
struct CharUtils {
    // Static utility methods
}

CharUtils {
    static fun is_letter(ch: string): bool {
        if ch.length() != 1 {
            return false;
        }
        
        let upper = ch.to_upper_case();
        let lower = ch.to_lower_case();
        return upper != lower;
    }
    
    static fun is_digit(ch: string): bool {
        if ch.length() != 1 {
            return false;
        }
        
        return ch >= "0" && ch <= "9";
    }
    
    static fun is_alphanumeric(ch: string): bool {
        return CharUtils::is_letter(ch) || CharUtils::is_digit(ch);
    }
    
    static fun is_whitespace(ch: string): bool {
        return ch == " " || ch == "\t" || ch == "\n" || ch == "\r";
    }
    
    static fun is_upper_case(ch: string): bool {
        if ch.length() != 1 {
            return false;
        }
        
        return ch >= "A" && ch <= "Z";
    }
    
    static fun is_lower_case(ch: string): bool {
        if ch.length() != 1 {
            return false;
        }
        
        return ch >= "a" && ch <= "z";
    }
    
    static fun is_punctuation(ch: string): bool {
        let punctuation = ".,;:!?\"'()[]{}";
        return punctuation.contains(ch);
    }
    
    static fun is_symbol(ch: string): bool {
        let symbols = "~`@#$%^&*-_+=|\\/<>";
        return symbols.contains(ch);
    }
    
    static fun to_upper_case(ch: string): string {
        if ch.length() != 1 {
            return ch;
        }
        
        if CharUtils::is_lower_case(ch) {
            return ch.to_upper_case();
        }
        
        return ch;
    }
    
    static fun to_lower_case(ch: string): string {
        if ch.length() != 1 {
            return ch;
        }
        
        if CharUtils::is_upper_case(ch) {
            return ch.to_lower_case();
        }
        
        return ch;
    }
    
    static fun get_char_type(ch: string): CharType {
        if CharUtils::is_letter(ch) {
            return CharType::Letter;
        } else if CharUtils::is_digit(ch) {
            return CharType::Digit;
        } else if CharUtils::is_whitespace(ch) {
            return CharType::Whitespace;
        } else if CharUtils::is_punctuation(ch) {
            return CharType::Punctuation;
        } else if CharUtils::is_symbol(ch) {
            return CharType::Symbol;
        } else {
            return CharType::Control;
        }
    }
    
    static fun is_vowel(ch: string): bool {
        let lower = ch.to_lower_case();
        return lower == "a" || lower == "e" || lower == "i" || lower == "o" || lower == "u";
    }
    
    static fun is_consonant(ch: string): bool {
        return CharUtils::is_letter(ch) && !CharUtils::is_vowel(ch);
    }
    
    static fun digit_to_number(ch: string): num {
        if !CharUtils::is_digit(ch) {
            panic("Character is not a digit: " + ch);
        }
        
        if ch == "0" { return 0; }
        if ch == "1" { return 1; }
        if ch == "2" { return 2; }
        if ch == "3" { return 3; }
        if ch == "4" { return 4; }
        if ch == "5" { return 5; }
        if ch == "6" { return 6; }
        if ch == "7" { return 7; }
        if ch == "8" { return 8; }
        if ch == "9" { return 9; }
        
        return 0; // Should never reach here
    }
    
    static fun number_to_digit(num: num): string {
        if num < 0 || num > 9 {
            panic("Number must be between 0 and 9: " + num.to_string());
        }
        
        if num == 0 { return "0"; }
        if num == 1 { return "1"; }
        if num == 2 { return "2"; }
        if num == 3 { return "3"; }
        if num == 4 { return "4"; }
        if num == 5 { return "5"; }
        if num == 6 { return "6"; }
        if num == 7 { return "7"; }
        if num == 8 { return "8"; }
        if num == 9 { return "9"; }
        
        return "0"; // Should never reach here
    }
    
    static fun compare(ch1: string, ch2: string): num {
        if ch1 == ch2 {
            return 0;
        } else if ch1 < ch2 {
            return -1;
        } else {
            return 1;
        }
    }
    
    static fun is_ascii(ch: string): bool {
        if ch.length() != 1 {
            return false;
        }
        
        // For simplicity, assume all single characters are ASCII
        // In a real implementation, we'd check the character code
        return true;
    }
    
    static fun escape_char(ch: string): string {
        if ch == "\n" { return "\\n"; }
        if ch == "\t" { return "\\t"; }
        if ch == "\r" { return "\\r"; }
        if ch == "\\" { return "\\\\"; }
        if ch == "\"" { return "\\\""; }
        if ch == "'" { return "\\'"; }
        
        return ch;
    }
    
    static fun unescape_char(escaped: string): string {
        if escaped == "\\n" { return "\n"; }
        if escaped == "\\t" { return "\t"; }
        if escaped == "\\r" { return "\r"; }
        if escaped == "\\\\" { return "\\"; }
        if escaped == "\\\"" { return "\""; }
        if escaped == "\\'" { return "'"; }
        
        return escaped;
    }
}

export { Char, CharType, CharConstants, CharUtils };
