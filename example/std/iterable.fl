// Interface cơ bản cho iteration trong Fluent Lang
// <PERSON><PERSON><PERSON> đích: <PERSON><PERSON><PERSON> dựng từ cơ bản để giảm phụ thuộc vào <PERSON>

// Interface cho các đối tượng có thể iterate
interface Iterable<T> {
    fun iterator(): Iterator<T>;
    fun length(): num;
    fun is_empty(): bool;
    fun is_not_empty(): bool;
}

// Interface cho iterator
interface Iterator<T> {
    fun hasNext(): bool;
    fun next(): T;
}

// Interface cho collection cơ bản
interface Collection<T> {
    fun add(item: T): void;
    fun remove(item: T): bool;
    fun contains(item: T): bool;
    fun clear(): void;
    fun length(): num;
    fun is_empty(): bool;
    fun is_not_empty(): bool;
}

export { Iterable, Iterator, Collection };
