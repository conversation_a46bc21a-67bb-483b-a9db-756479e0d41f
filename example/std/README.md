# 📚 Thư viện std cơ bản cho Fluent Lang

## 🎯 <PERSON><PERSON><PERSON> đích

Xây dựng thư viện chuẩn từ cơ bản để giảm phụ thuộc và<PERSON>, tạo nền tảng vững chắc cho ngôn ngữ Fluent Lang.

## 📦 Cấu trúc thư viện

### **1. char.fl** - <PERSON><PERSON>u ký tự cơ bản
```fluent
struct char {
    ascii_code: num; // Mã ASCII từ 0-127
}
```

**Tính năng:**
- ✅ Lưu trữ mã ASCII thay vì string
- ✅ Tạo char từ ASCII code, chữ số, chữ cái
- ✅ Kiểm tra loại ký tự (letter, digit, whitespace, etc.)
- ✅ Chuyển đổi case (upper/lower)
- ✅ So sánh char
- ✅ Chuyển đổi chữ số thành số

### **2. string.fl** - Chuỗi các char
```fluent
struct String {
    chars: Array<char>; // Mảng các char
}
```

**T<PERSON>h năng:**
- ✅ String là chuỗi các char (không phụ thuộc Dart string)
- ✅ Tạo từ char, số, mảng char
- ✅ Thao tác cơ bản: append, substring, trim
- ✅ Tìm kiếm: index_of, contains, starts_with, ends_with
- ✅ Chuyển đổi case
- ✅ So sánh string
- ✅ Iteration support

### **3. array.fl** - Mảng cơ bản
```fluent
struct Array<T> {
    data: Array<T>; // Tạm thời dùng Dart Array
}
```

**Tính năng:**
- ✅ Generic array cho mọi kiểu dữ liệu
- ✅ Thao tác cơ bản: add, get, set, remove
- ✅ Tìm kiếm: index_of, contains
- ✅ Slice và copy
- ✅ Iteration support

### **4. string_builder.fl** - Xây dựng string hiệu quả
```fluent
struct StringBuilder {
    parts: Array<String>; // Mảng các phần string
}
```

**Tính năng:**
- ✅ Xây dựng string hiệu quả
- ✅ Append string, char, number, boolean
- ✅ Insert, remove, replace
- ✅ Fluent API pattern

### **5. iterable.fl** - Interface iteration
```fluent
interface Iterable<T> {
    fun iterator(): Iterator<T>;
    fun length(): num;
    fun is_empty(): bool;
}

interface Iterator<T> {
    fun hasNext(): bool;
    fun next(): T;
}
```

**Tính năng:**
- ✅ Interface chuẩn cho iteration
- ✅ Support cho String và Array
- ✅ Collection interface

## 🚀 Ví dụ sử dụng

### **Làm việc với char**
```fluent
import "std/char.fl" show char, AsciiConstants;

fun demo_char(): void {
    // Tạo char từ ASCII
    let ch_a = char::from_ascii(65); // 'A'
    print("ASCII code: " + ch_a.get_ascii_code().to_string());
    
    // Kiểm tra loại
    print("Is letter: " + ch_a.is_letter().to_string());
    print("Is upper: " + ch_a.is_upper_case().to_string());
    
    // Chuyển đổi
    let lower = ch_a.to_lower_case();
    print("Lower ASCII: " + lower.get_ascii_code().to_string());
    
    // Từ chữ số
    let digit = char::from_digit(5);
    print("Digit value: " + digit.to_digit().to_string());
}
```

### **Làm việc với String**
```fluent
import "std/string.fl" show String;
import "std/char.fl" show char;

fun demo_string(): void {
    // Tạo string từ char
    let hello = String::from_char(char::from_ascii(72)) // 'H'
        .append_char(char::from_ascii(101)) // 'e'
        .append_char(char::from_ascii(108)) // 'l'
        .append_char(char::from_ascii(108)) // 'l'
        .append_char(char::from_ascii(111)); // 'o'
    
    print("Length: " + hello.length().to_string());
    
    // Thao tác string
    let upper = hello.to_upper_case();
    let sub = hello.substring(0, 2);
    
    // Từ số
    let num_str = String::from_number(123);
    let combined = hello.append(num_str);
}
```

### **Làm việc với StringBuilder**
```fluent
import "std/string_builder.fl" show StringBuilder;
import "std/string.fl" show String;

fun demo_builder(): void {
    let builder = StringBuilder::new();
    
    builder.append(String::from_number(2024))
           .append_space()
           .append_char(char::from_ascii(45)) // '-'
           .append_number(12)
           .append_char(char::from_ascii(45)) // '-'
           .append_number(31);
    
    let result = builder.to_string();
    print("Date: " + result.length().to_string() + " chars");
}
```

## 🔄 Migration từ thư viện cũ

### **Trước (phụ thuộc Dart)**
```fluent
import "char.fl" show CharUtils;
import "string_lib.fl" show FluentString;

let ch = "A"; // Dart string
let fs = FluentString::new("Hello"); // Wrapper Dart string
```

### **Sau (độc lập)**
```fluent
import "std/char.fl" show char;
import "std/string.fl" show String;

let ch = char::from_ascii(65); // Pure ASCII
let str = String::from_char(ch); // Pure char array
```

## ✅ Lợi ích

1. **Độc lập hoàn toàn** - Không phụ thuộc vào Dart string
2. **Hiệu suất cao** - Làm việc trực tiếp với ASCII codes
3. **Type safety** - Strong typing với char và String riêng biệt
4. **Mở rộng dễ dàng** - Có thể thêm encoding khác (UTF-8, UTF-16)
5. **Memory efficient** - Kiểm soát hoàn toàn memory layout
6. **Bootstrap ready** - Sẵn sàng cho self-hosting compiler

## 🎯 Roadmap

- [ ] **Encoding support** - UTF-8, UTF-16 cho char
- [ ] **Regex engine** - Pattern matching cơ bản
- [ ] **String formatting** - Printf-style formatting
- [ ] **Locale support** - Internationalization
- [ ] **Performance optimization** - Memory pooling, copy-on-write
- [ ] **File I/O** - Reading/writing text files

## 🚀 Bootstrap Impact

Thư viện std cơ bản này là bước quan trọng để Fluent Lang trở nên độc lập:

- **Text Processing** ✅ - Xử lý source code không cần Dart
- **Code Generation** ✅ - StringBuilder cho compiler output  
- **Self-hosting** ✅ - Compiler có thể viết bằng chính Fluent Lang
- **Performance** ✅ - Kiểm soát hoàn toàn memory và processing

**Bootstrap Readiness: +3 points** 🎉

Fluent Lang giờ đã có foundation vững chắc để xây dựng compiler và tools hoàn toàn độc lập! 🚀
