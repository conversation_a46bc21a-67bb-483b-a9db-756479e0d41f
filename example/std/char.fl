// <PERSON>ểu char cơ bản cho Fluent Lang - chỉ chứa mã ASCII
// Mục đích: <PERSON><PERSON><PERSON> dựng từ cơ bản để giảm phụ thuộc vào Dart

// <PERSON>ểu char cơ bản - chỉ chứa mã ASCII (0-127)
struct char {
    ascii_code: num; // Mã ASCII từ 0 đến 127
}

// Enum cho loại ký tự
enum CharType {
    Letter,
    Digit, 
    Whitespace,
    Punctuation,
    Symbol,
    Control
}

// Hằng số ASCII
struct AsciiConstants {
    // Static constants
}

AsciiConstants {
    // Ký tự điều khiển
    static fun null_char(): num { return 0; }
    static fun tab(): num { return 9; }
    static fun newline(): num { return 10; }
    static fun carriage_return(): num { return 13; }
    static fun space(): num { return 32; }
    
    // Chữ số
    static fun ascii_zero(): num { return 48; }  // '0'
    static fun ascii_nine(): num { return 57; }  // '9'
    
    // Chữ cái hoa
    static fun ascii_upper_a(): num { return 65; }  // 'A'
    static fun ascii_upper_z(): num { return 90; }  // 'Z'
    
    // Chữ cái thường
    static fun ascii_lower_a(): num { return 97; }  // 'a'
    static fun ascii_lower_z(): num { return 122; } // 'z'
    
    // Ký tự đặc biệt
    static fun exclamation(): num { return 33; }  // '!'
    static fun question(): num { return 63; }     // '?'
    static fun period(): num { return 46; }       // '.'
    static fun comma(): num { return 44; }        // ','
}

// Triển khai char
char {
    // Tạo char từ mã ASCII
    static fun from_ascii(code: num): char {
        if code < 0 || code > 127 {
            panic("ASCII code must be between 0 and 127");
        }
        return char { ascii_code: code };
    }
    
    // Tạo char từ chữ số
    static fun from_digit(digit: num): char {
        if digit < 0 || digit > 9 {
            panic("Digit must be between 0 and 9");
        }
        return char::from_ascii(AsciiConstants::ascii_zero() + digit);
    }
    
    // Lấy mã ASCII
    fun get_ascii_code(): num {
        return this.ascii_code;
    }
    
    // Kiểm tra loại ký tự
    fun is_letter(): bool {
        return (this.ascii_code >= AsciiConstants::ascii_upper_a() && 
                this.ascii_code <= AsciiConstants::ascii_upper_z()) ||
               (this.ascii_code >= AsciiConstants::ascii_lower_a() && 
                this.ascii_code <= AsciiConstants::ascii_lower_z());
    }
    
    fun is_digit(): bool {
        return this.ascii_code >= AsciiConstants::ascii_zero() && 
               this.ascii_code <= AsciiConstants::ascii_nine();
    }
    
    fun is_upper_case(): bool {
        return this.ascii_code >= AsciiConstants::ascii_upper_a() && 
               this.ascii_code <= AsciiConstants::ascii_upper_z();
    }
    
    fun is_lower_case(): bool {
        return this.ascii_code >= AsciiConstants::ascii_lower_a() && 
               this.ascii_code <= AsciiConstants::ascii_lower_z();
    }
    
    fun is_whitespace(): bool {
        return this.ascii_code == AsciiConstants::space() ||
               this.ascii_code == AsciiConstants::tab() ||
               this.ascii_code == AsciiConstants::newline() ||
               this.ascii_code == AsciiConstants::carriage_return();
    }
    
    fun is_alphanumeric(): bool {
        return this.is_letter() || this.is_digit();
    }
    
    // Chuyển đổi case
    fun to_upper_case(): char {
        if this.is_lower_case() {
            let offset = AsciiConstants::ascii_upper_a() - AsciiConstants::ascii_lower_a();
            return char::from_ascii(this.ascii_code + offset);
        }
        return this;
    }
    
    fun to_lower_case(): char {
        if this.is_upper_case() {
            let offset = AsciiConstants::ascii_lower_a() - AsciiConstants::ascii_upper_a();
            return char::from_ascii(this.ascii_code + offset);
        }
        return this;
    }
    
    // Chuyển đổi chữ số thành số
    fun to_digit(): num {
        if !this.is_digit() {
            panic("Character is not a digit");
        }
        return this.ascii_code - AsciiConstants::ascii_zero();
    }
    
    // So sánh
    fun equals(other: char): bool {
        return this.ascii_code == other.ascii_code;
    }
    
    fun compare_to(other: char): num {
        if this.ascii_code == other.ascii_code {
            return 0;
        } else if this.ascii_code < other.ascii_code {
            return -1;
        } else {
            return 1;
        }
    }
    
    // Lấy loại ký tự
    fun get_char_type(): CharType {
        if this.is_letter() {
            return CharType::Letter;
        } else if this.is_digit() {
            return CharType::Digit;
        } else if this.is_whitespace() {
            return CharType::Whitespace;
        } else if this.is_punctuation() {
            return CharType::Punctuation;
        } else if this.is_symbol() {
            return CharType::Symbol;
        } else {
            return CharType::Control;
        }
    }
    
    fun is_punctuation(): bool {
        return this.ascii_code == 33 ||  // !
               this.ascii_code == 34 ||  // "
               this.ascii_code == 39 ||  // '
               this.ascii_code == 40 ||  // (
               this.ascii_code == 41 ||  // )
               this.ascii_code == 44 ||  // ,
               this.ascii_code == 46 ||  // .
               this.ascii_code == 58 ||  // :
               this.ascii_code == 59 ||  // ;
               this.ascii_code == 63 ||  // ?
               this.ascii_code == 91 ||  // [
               this.ascii_code == 93 ||  // ]
               this.ascii_code == 123 || // {
               this.ascii_code == 125;   // }
    }
    
    fun is_symbol(): bool {
        return (this.ascii_code >= 35 && this.ascii_code <= 38) ||  // # $ % &
               (this.ascii_code >= 42 && this.ascii_code <= 43) ||  // * +
               this.ascii_code == 45 ||  // -
               this.ascii_code == 47 ||  // /
               (this.ascii_code >= 60 && this.ascii_code <= 62) ||  // < = >
               this.ascii_code == 64 ||  // @
               this.ascii_code == 92 ||  // \
               this.ascii_code == 94 ||  // ^
               this.ascii_code == 95 ||  // _
               this.ascii_code == 96 ||  // `
               this.ascii_code == 124 || // |
               this.ascii_code == 126;   // ~
    }
}

export { char, CharType, AsciiConstants };
