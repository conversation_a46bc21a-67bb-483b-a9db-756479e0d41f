// Struct String cho Fluent Lang - chuỗi các char
// <PERSON><PERSON><PERSON> đích: X<PERSON><PERSON> dựng từ cơ bản để giảm phụ thuộc vào Dart

import "iterable.fl" show Iterable, Iterator;
import "array.fl" show Array, ArrayIterator;
import "char.fl" show char, CharType, AsciiConstants;

// Struct String - chuỗi các char
struct String {
    chars: Array<char>; // Mảng các char
}

// Iterator cho String
struct StringIterator {
    string: String;
    index: num;
}

// Triển khai String
String {
    // Tạo string rỗng
    static fun new(): String {
        let chars = Array<char>::new();
        return String { chars: chars };
    }

    // Tạo string từ mảng char
    static fun from_chars(chars: Array<char>): String {
        return String { chars: chars.copy() };
    }

    // Tạo string từ một char duy nhất
    static fun from_char(ch: char): String {
        let chars = Array<char>::new();
        chars.add(ch);
        return String { chars: chars };
    }

    // Tạo string từ chuỗi các chữ số
    static fun from_number(num: num): String {
        if num == 0 {
            let chars = Array<char>::new();
            chars.add(char::from_digit(0));
            return String { chars: chars };
        }

        let chars = Array<char>::new();
        let temp = num;
        let is_negative = false;

        if temp < 0 {
            is_negative = true;
            temp = -temp;
        }

        // Chuyển đổi từng chữ số
        while temp > 0 {
            let digit = temp % 10;
            chars.insert(0, char::from_digit(digit));
            temp = temp / 10;
        }

        // Thêm dấu âm nếu cần
        if is_negative {
            chars.insert(0, char::from_ascii(45)); // '-'
        }

        return String { chars: chars };
    }

    // Lấy độ dài
    fun length(): num {
        return this.chars.length();
    }

    // Kiểm tra rỗng
    fun is_empty(): bool {
        return this.chars.is_empty();
    }

    fun is_not_empty(): bool {
        return this.chars.is_not_empty();
    }

    // Lấy char tại vị trí
    fun char_at(index: num): char {
        if index < 0 || index >= this.length() {
            panic("Index out of bounds: " + index.to_string());
        }
        return this.chars.get(index);
    }

    // Thêm char vào cuối
    fun append_char(ch: char): String {
        let new_chars = this.chars.copy();
        new_chars.add(ch);
        return String { chars: new_chars };
    }

    // Thêm string vào cuối
    fun append(other: String): String {
        let new_chars = this.chars.copy();
        let i = 0;
        while i < other.length() {
            new_chars.add(other.char_at(i));
            i = i + 1;
        }
        return String { chars: new_chars };
    }

    // Lấy substring
    fun substring(start: num, end: num): String {
        if start < 0 || start > this.length() {
            panic("Start index out of bounds: " + start.to_string());
        }
        if end < start || end > this.length() {
            panic("End index out of bounds: " + end.to_string());
        }

        let new_chars = Array<char>::new();
        let i = start;
        while i < end {
            new_chars.add(this.char_at(i));
            i = i + 1;
        }
        return String { chars: new_chars };
    }

    // Tìm vị trí của char
    fun index_of_char(ch: char): num {
        let i = 0;
        while i < this.length() {
            if this.char_at(i).equals(ch) {
                return i;
            }
            i = i + 1;
        }
        return -1;
    }

    // Kiểm tra chứa char
    fun contains_char(ch: char): bool {
        return this.index_of_char(ch) != -1;
    }

    // Kiểm tra bắt đầu bằng
    fun starts_with(prefix: String): bool {
        if prefix.length() > this.length() {
            return false;
        }

        let i = 0;
        while i < prefix.length() {
            if !this.char_at(i).equals(prefix.char_at(i)) {
                return false;
            }
            i = i + 1;
        }
        return true;
    }

    // Kiểm tra kết thúc bằng
    fun ends_with(suffix: String): bool {
        if suffix.length() > this.length() {
            return false;
        }

        let start = this.length() - suffix.length();
        let i = 0;
        while i < suffix.length() {
            if !this.char_at(start + i).equals(suffix.char_at(i)) {
                return false;
            }
            i = i + 1;
        }
        return true;
    }

    // Chuyển thành chữ hoa
    fun to_upper_case(): String {
        let new_chars = Array<char>::new();
        let i = 0;
        while i < this.length() {
            new_chars.add(this.char_at(i).to_upper_case());
            i = i + 1;
        }
        return String { chars: new_chars };
    }

    // Chuyển thành chữ thường
    fun to_lower_case(): String {
        let new_chars = Array<char>::new();
        let i = 0;
        while i < this.length() {
            new_chars.add(this.char_at(i).to_lower_case());
            i = i + 1;
        }
        return String { chars: new_chars };
    }

    // Loại bỏ khoảng trắng đầu và cuối
    fun trim(): String {
        if this.is_empty() {
            return this;
        }

        let start = 0;
        let end = this.length();

        // Tìm vị trí bắt đầu (bỏ qua whitespace)
        while start < end && this.char_at(start).is_whitespace() {
            start = start + 1;
        }

        // Tìm vị trí kết thúc (bỏ qua whitespace)
        while end > start && this.char_at(end - 1).is_whitespace() {
            end = end - 1;
        }

        return this.substring(start, end);
    }

    // So sánh với string khác
    fun equals(other: String): bool {
        if this.length() != other.length() {
            return false;
        }

        let i = 0;
        while i < this.length() {
            if !this.char_at(i).equals(other.char_at(i)) {
                return false;
            }
            i = i + 1;
        }
        return true;
    }

    fun compare_to(other: String): num {
        let min_length = this.length();
        if other.length() < min_length {
            min_length = other.length();
        }

        let i = 0;
        while i < min_length {
            let cmp = this.char_at(i).compare_to(other.char_at(i));
            if cmp != 0 {
                return cmp;
            }
            i = i + 1;
        }

        // Nếu tất cả char giống nhau, so sánh độ dài
        if this.length() == other.length() {
            return 0;
        } else if this.length() < other.length() {
            return -1;
        } else {
            return 1;
        }
    }

    // Đảo ngược string
    fun reverse(): String {
        let new_chars = Array<char>::new();
        let i = this.length() - 1;
        while i >= 0 {
            new_chars.add(this.char_at(i));
            i = i - 1;
        }
        return String { chars: new_chars };
    }

    // Lặp lại string
    fun repeat(count: num): String {
        if count <= 0 {
            return String::new();
        }

        let result = String::new();
        let i = 0;
        while i < count {
            result = result.append(this);
            i = i + 1;
        }
        return result;
    }
}

// Triển khai Iterable cho String
String: Iterable<char> {
    fun iterator(): Iterator<char> {
        return StringIterator::new(this);
    }
}

// Triển khai StringIterator
StringIterator {
    static fun new(string: String): StringIterator {
        return StringIterator { string: string, index: 0 };
    }
}

StringIterator: Iterator<char> {
    fun hasNext(): bool {
        return this.index < this.string.length();
    }

    fun next(): char {
        if !this.hasNext() {
            panic("No more characters");
        }
        let ch = this.string.char_at(this.index);
        this.index = this.index + 1;
        return ch;
    }
}

export { String, StringIterator };