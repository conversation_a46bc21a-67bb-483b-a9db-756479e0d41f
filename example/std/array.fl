// Array cơ bản cho Fluent Lang
// <PERSON>ục đích: Hỗ trợ cho struct String

import "iterable.fl" show Iterable, Iterator;

// Array cơ bản để lưu trữ các phần tử
struct Array<T> {
    data: Array<T>; // Sử dụng Array built-in của Dart tạm thời
}

// Iterator cho Array
struct ArrayIterator<T> {
    array: Array<T>;
    index: num;
}

// Triển khai Array
Array<T> {
    // Tạo array rỗng
    static fun new(): Array<T> {
        let data = Array<T>();
        return Array<T> { data: data };
    }
    
    // Tạo array với kích thước cố định
    static fun with_capacity(capacity: num): Array<T> {
        let data = Array<T>();
        return Array<T> { data: data };
    }
    
    // Thêm phần tử
    fun add(item: T): void {
        this.data.add(item);
    }
    
    // Lấy phần tử tại vị trí
    fun get(index: num): T {
        if index < 0 || index >= this.length() {
            panic("Index out of bounds: " + index.to_string());
        }
        return this.data.get(index);
    }
    
    // Đặt phần tử tại vị trí
    fun set(index: num, item: T): void {
        if index < 0 || index >= this.length() {
            panic("Index out of bounds: " + index.to_string());
        }
        this.data.set(index, item);
    }
    
    // Lấy độ dài
    fun length(): num {
        return this.data.length();
    }
    
    // Kiểm tra rỗng
    fun is_empty(): bool {
        return this.data.length() == 0;
    }
    
    fun is_not_empty(): bool {
        return this.data.length() > 0;
    }
    
    // Xóa tất cả
    fun clear(): void {
        this.data.clear();
    }
    
    // Xóa phần tử tại vị trí
    fun remove_at(index: num): T {
        if index < 0 || index >= this.length() {
            panic("Index out of bounds: " + index.to_string());
        }
        return this.data.remove_at(index);
    }
    
    // Chèn phần tử tại vị trí
    fun insert(index: num, item: T): void {
        if index < 0 || index > this.length() {
            panic("Index out of bounds: " + index.to_string());
        }
        this.data.insert(index, item);
    }
    
    // Tìm vị trí của phần tử
    fun index_of(item: T): num {
        let i = 0;
        while i < this.length() {
            if this.get(i) == item {
                return i;
            }
            i = i + 1;
        }
        return -1;
    }
    
    // Kiểm tra chứa phần tử
    fun contains(item: T): bool {
        return this.index_of(item) != -1;
    }
    
    // Sao chép array
    fun copy(): Array<T> {
        let result = Array<T>::new();
        let i = 0;
        while i < this.length() {
            result.add(this.get(i));
            i = i + 1;
        }
        return result;
    }
    
    // Lấy sub-array
    fun slice(start: num, end: num): Array<T> {
        if start < 0 || start > this.length() {
            panic("Start index out of bounds: " + start.to_string());
        }
        if end < start || end > this.length() {
            panic("End index out of bounds: " + end.to_string());
        }
        
        let result = Array<T>::new();
        let i = start;
        while i < end {
            result.add(this.get(i));
            i = i + 1;
        }
        return result;
    }
}

// Triển khai Iterable cho Array
Array<T>: Iterable<T> {
    fun iterator(): Iterator<T> {
        return ArrayIterator<T>::new(this);
    }
}

// Triển khai ArrayIterator
ArrayIterator<T> {
    static fun new(array: Array<T>): ArrayIterator<T> {
        return ArrayIterator<T> { array: array, index: 0 };
    }
}

ArrayIterator<T>: Iterator<T> {
    fun hasNext(): bool {
        return this.index < this.array.length();
    }
    
    fun next(): T {
        if !this.hasNext() {
            panic("No more elements");
        }
        let item = this.array.get(this.index);
        this.index = this.index + 1;
        return item;
    }
}

export { Array, ArrayIterator };
