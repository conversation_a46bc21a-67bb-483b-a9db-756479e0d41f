// StringBuilder cho Fluent Lang - xây dựng string hiệu quả
// M<PERSON><PERSON> đích: <PERSON><PERSON><PERSON> dựng từ cơ bản để giảm phụ thuộc vào <PERSON>t

import "array.fl" show Array;
import "string.fl" show String;
import "char.fl" show char, AsciiConstants;

// StringBuilder để xây dựng string hiệu quả
struct StringBuilder {
    parts: Array<String>; // Mảng các phần string
}

StringBuilder {
    // Tạo StringBuilder mới
    static fun new(): StringBuilder {
        let parts = Array<String>::new();
        return StringBuilder { parts: parts };
    }
    
    // Thêm string
    fun append(text: String): StringBuilder {
        this.parts.add(text);
        return this;
    }
    
    // Thêm char
    fun append_char(ch: char): StringBuilder {
        let str = String::from_char(ch);
        this.parts.add(str);
        return this;
    }
    
    // Thêm số
    fun append_number(num: num): StringBuilder {
        let str = String::from_number(num);
        this.parts.add(str);
        return this;
    }
    
    // Thêm boolean
    fun append_bool(value: bool): StringBuilder {
        let str = if value {
            String::from_chars(Array<char>::new()
                .add(char::from_ascii(116)) // 't'
                .add(char::from_ascii(114)) // 'r'
                .add(char::from_ascii(117)) // 'u'
                .add(char::from_ascii(101))); // 'e'
        } else {
            String::from_chars(Array<char>::new()
                .add(char::from_ascii(102)) // 'f'
                .add(char::from_ascii(97))  // 'a'
                .add(char::from_ascii(108)) // 'l'
                .add(char::from_ascii(115)) // 's'
                .add(char::from_ascii(101))); // 'e'
        };
        this.parts.add(str);
        return this;
    }
    
    // Thêm dòng mới
    fun append_line(text: String): StringBuilder {
        this.parts.add(text);
        this.parts.add(String::from_char(char::newline()));
        return this;
    }
    
    // Thêm space
    fun append_space(): StringBuilder {
        this.parts.add(String::from_char(char::space()));
        return this;
    }
    
    // Thêm tab
    fun append_tab(): StringBuilder {
        this.parts.add(String::from_char(char::tab()));
        return this;
    }
    
    // Chèn string tại vị trí
    fun insert(index: num, text: String): StringBuilder {
        if index < 0 || index > this.parts.length() {
            panic("Index out of bounds: " + index.to_string());
        }
        this.parts.insert(index, text);
        return this;
    }
    
    // Xóa tất cả
    fun clear(): StringBuilder {
        this.parts.clear();
        return this;
    }
    
    // Lấy độ dài tổng
    fun length(): num {
        let total = 0;
        let i = 0;
        while i < this.parts.length() {
            total = total + this.parts.get(i).length();
            i = i + 1;
        }
        return total;
    }
    
    // Kiểm tra rỗng
    fun is_empty(): bool {
        return this.parts.length() == 0;
    }
    
    fun is_not_empty(): bool {
        return this.parts.length() > 0;
    }
    
    // Chuyển thành String
    fun to_string(): String {
        if this.parts.is_empty() {
            return String::new();
        }
        
        let result = this.parts.get(0);
        let i = 1;
        while i < this.parts.length() {
            result = result.append(this.parts.get(i));
            i = i + 1;
        }
        return result;
    }
    
    // Sao chép StringBuilder
    fun copy(): StringBuilder {
        let new_builder = StringBuilder::new();
        let i = 0;
        while i < this.parts.length() {
            new_builder.parts.add(this.parts.get(i));
            i = i + 1;
        }
        return new_builder;
    }
    
    // Xóa phần tử tại vị trí
    fun remove_at(index: num): StringBuilder {
        if index < 0 || index >= this.parts.length() {
            panic("Index out of bounds: " + index.to_string());
        }
        this.parts.remove_at(index);
        return this;
    }
    
    // Thay thế phần tử tại vị trí
    fun replace_at(index: num, text: String): StringBuilder {
        if index < 0 || index >= this.parts.length() {
            panic("Index out of bounds: " + index.to_string());
        }
        this.parts.set(index, text);
        return this;
    }
    
    // Lấy số lượng phần
    fun part_count(): num {
        return this.parts.length();
    }
    
    // Lấy phần tại vị trí
    fun get_part(index: num): String {
        if index < 0 || index >= this.parts.length() {
            panic("Index out of bounds: " + index.to_string());
        }
        return this.parts.get(index);
    }
}

export { StringBuilder };
