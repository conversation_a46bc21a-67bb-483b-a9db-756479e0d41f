// Demo cho thư viện std cơ bản của Fluent Lang
// <PERSON><PERSON><PERSON> đích: Test các kiểu char, String, Array, StringBuilder cơ bản

import "std/char.fl" show char, CharType, AsciiConstants;
import "std/string.fl" show String, StringIterator;
import "std/array.fl" show Array, ArrayIterator;
import "std/string_builder.fl" show StringBuilder;
import "std/iterable.fl" show Iterable, Iterator;

fun test_char(): void {
    print("=== Test char cơ bản ===");
    
    // Tạo char từ ASCII
    let ch_a = char::from_ascii(65); // 'A'
    print("Char A ASCII code: " + ch_a.get_ascii_code().to_string());
    print("Is letter: " + ch_a.is_letter().to_string());
    print("Is upper case: " + ch_a.is_upper_case().to_string());
    
    // Chuyển đổi case
    let ch_lower = ch_a.to_lower_case();
    print("To lower case ASCII: " + ch_lower.get_ascii_code().to_string());
    
    // Tạo char từ chữ số
    let ch_5 = char::from_digit(5);
    print("Digit 5 ASCII code: " + ch_5.get_ascii_code().to_string());
    print("Is digit: " + ch_5.is_digit().to_string());
    print("To digit: " + ch_5.to_digit().to_string());
    
    // Char đặc biệt
    let space = char::space();
    let newline = char::newline();
    print("Space ASCII: " + space.get_ascii_code().to_string());
    print("Newline ASCII: " + newline.get_ascii_code().to_string());
    print("Space is whitespace: " + space.is_whitespace().to_string());
}

fun test_string(): void {
    print("\n=== Test String cơ bản ===");
    
    // Tạo string rỗng
    let empty_str = String::new();
    print("Empty string length: " + empty_str.length().to_string());
    print("Is empty: " + empty_str.is_empty().to_string());
    
    // Tạo string từ char
    let ch_h = char::from_ascii(72); // 'H'
    let str_h = String::from_char(ch_h);
    print("String from char H length: " + str_h.length().to_string());
    
    // Thêm char vào string
    let ch_i = char::from_ascii(105); // 'i'
    let str_hi = str_h.append_char(ch_i);
    print("String Hi length: " + str_hi.length().to_string());
    
    // Tạo string từ số
    let num_str = String::from_number(123);
    print("String from number 123 length: " + num_str.length().to_string());
    
    // Test substring
    let sub = str_hi.substring(0, 1);
    print("Substring [0,1) length: " + sub.length().to_string());
    
    // Test append
    let hello = str_hi.append(num_str);
    print("Hello + 123 length: " + hello.length().to_string());
    
    // Test case conversion
    let upper = str_hi.to_upper_case();
    print("Hi to upper case - first char ASCII: " + upper.char_at(0).get_ascii_code().to_string());
    
    // Test trim
    let space_str = String::from_char(char::space());
    let with_spaces = space_str.append(str_hi).append(space_str);
    let trimmed = with_spaces.trim();
    print("Trimmed length: " + trimmed.length().to_string());
}

fun test_array(): void {
    print("\n=== Test Array cơ bản ===");
    
    // Tạo array char
    let char_array = Array<char>::new();
    print("Empty array length: " + char_array.length().to_string());
    
    // Thêm char
    char_array.add(char::from_ascii(65)); // 'A'
    char_array.add(char::from_ascii(66)); // 'B'
    char_array.add(char::from_ascii(67)); // 'C'
    print("Array with 3 chars length: " + char_array.length().to_string());
    
    // Lấy char
    let first_char = char_array.get(0);
    print("First char ASCII: " + first_char.get_ascii_code().to_string());
    
    // Test contains
    let ch_a = char::from_ascii(65);
    print("Contains A: " + char_array.contains(ch_a).to_string());
    
    // Test index_of
    let index = char_array.index_of(ch_a);
    print("Index of A: " + index.to_string());
    
    // Test slice
    let slice = char_array.slice(0, 2);
    print("Slice [0,2) length: " + slice.length().to_string());
}

fun test_string_builder(): void {
    print("\n=== Test StringBuilder ===");
    
    // Tạo StringBuilder
    let builder = StringBuilder::new();
    print("Empty builder length: " + builder.length().to_string());
    
    // Thêm string
    let hello_str = String::from_char(char::from_ascii(72)) // 'H'
        .append_char(char::from_ascii(101)) // 'e'
        .append_char(char::from_ascii(108)) // 'l'
        .append_char(char::from_ascii(108)) // 'l'
        .append_char(char::from_ascii(111)); // 'o'
    
    builder.append(hello_str);
    builder.append_space();
    
    let world_str = String::from_char(char::from_ascii(87)) // 'W'
        .append_char(char::from_ascii(111)) // 'o'
        .append_char(char::from_ascii(114)) // 'r'
        .append_char(char::from_ascii(108)) // 'l'
        .append_char(char::from_ascii(100)); // 'd'
    
    builder.append(world_str);
    builder.append_char(char::from_ascii(33)); // '!'
    
    print("Builder part count: " + builder.part_count().to_string());
    print("Builder total length: " + builder.length().to_string());
    
    // Thêm số
    builder.append_space();
    builder.append_number(2024);
    
    print("After adding number, length: " + builder.length().to_string());
    
    // Test clear
    let builder2 = StringBuilder::new();
    builder2.append(hello_str);
    builder2.clear();
    print("After clear, length: " + builder2.length().to_string());
}

fun test_iteration(): void {
    print("\n=== Test Iteration ===");
    
    // Tạo string để iterate
    let str = String::from_char(char::from_ascii(65)) // 'A'
        .append_char(char::from_ascii(66)) // 'B'
        .append_char(char::from_ascii(67)); // 'C'
    
    print("String length: " + str.length().to_string());
    
    // Iterate qua string
    let iter = str.iterator();
    let count = 0;
    while iter.hasNext() {
        let ch = iter.next();
        print("Char " + count.to_string() + " ASCII: " + ch.get_ascii_code().to_string());
        count = count + 1;
    }
    
    print("Total chars iterated: " + count.to_string());
}

fun main(): void {
    print("🚀 Demo thư viện std cơ bản của Fluent Lang");
    print("Mục đích: Xây dựng từ cơ bản để giảm phụ thuộc vào Dart\n");
    
    test_char();
    test_string();
    test_array();
    test_string_builder();
    test_iteration();
    
    print("\n✅ Hoàn thành test các thư viện std cơ bản!");
}

main();
