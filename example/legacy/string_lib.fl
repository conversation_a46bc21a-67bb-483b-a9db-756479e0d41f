// Enhanced String library for Fluent Lang with Iterable support

import "iterable.fl" show Iterable, Iterator, IterableUtils;
import "char.fl" show <PERSON><PERSON><PERSON><PERSON><PERSON>, CharType;

// String wrapper with enhanced functionality
struct FluentString {
    value: string;
}

// String iterator for character-by-character iteration
struct StringIterator {
    text: string;
    index: num;
}

// String builder for efficient string construction
struct StringBuilder {
    parts: Array<string>;
}

// String comparison result
enum StringComparison {
    Less,
    Equal,
    Greater
}

// String case style
enum CaseStyle {
    CamelCase,
    PascalCase,
    SnakeCase,
    KebabCase,
    UpperCase,
    LowerCase
}

// Implement Iterable for FluentString
FluentString: Iterable<string> {
    fun new(text: string): FluentString {
        return FluentString { value: text };
    }

    fun iterator(): Iterator<string> {
        return StringIterator::new(this.value);
    }

    fun length(): num {
        return this.value.length();
    }

    fun is_empty(): bool {
        return this.value.is_empty();
    }

    fun is_not_empty(): bool {
        return this.value.is_not_empty();
    }

    // Enhanced string methods
    fun char_at(index: num): string {
        return this.value.char_at(index);
    }

    fun substring(start: num, end: num): FluentString {
        return FluentString::new(this.value.substring(start, end));
    }

    fun index_of(search: string): num {
        return this.value.index_of(search);
    }

    fun last_index_of(search: string): num {
        let index = -1;
        let current = 0;

        while current < this.value.length() {
            let found = this.value.substring(current).index_of(search);
            if found == -1 {
                break;
            }
            index = current + found;
            current = current + found + 1;
        }

        return index;
    }

    fun contains(search: string): bool {
        return this.value.contains(search);
    }

    fun starts_with(prefix: string): bool {
        return this.value.starts_with(prefix);
    }

    fun ends_with(suffix: string): bool {
        return this.value.ends_with(suffix);
    }

    fun to_upper_case(): FluentString {
        return FluentString::new(this.value.to_upper_case());
    }

    fun to_lower_case(): FluentString {
        return FluentString::new(this.value.to_lower_case());
    }

    fun trim(): FluentString {
        return FluentString::new(this.value.trim());
    }

    fun trim_start(): FluentString {
        let text = this.value;
        let start = 0;

        while start < text.length() && CharUtils::is_whitespace(text.char_at(start)) {
            start = start + 1;
        }

        return FluentString::new(text.substring(start));
    }

    fun trim_end(): FluentString {
        let text = this.value;
        let end = text.length();

        while end > 0 && CharUtils::is_whitespace(text.char_at(end - 1)) {
            end = end - 1;
        }

        return FluentString::new(text.substring(0, end));
    }

    fun replace(from: string, to: string): FluentString {
        return FluentString::new(this.value.replace(from, to));
    }

    fun replace_first(from: string, to: string): FluentString {
        let index = this.value.index_of(from);
        if index == -1 {
            return FluentString::new(this.value);
        }

        let before = this.value.substring(0, index);
        let after = this.value.substring(index + from.length());
        return FluentString::new(before + to + after);
    }

    fun split(separator: string): Array<FluentString> {
        let parts = this.value.split(separator);
        let result = Array<FluentString>();

        let i = 0;
        while i < parts.length() {
            result.add(FluentString::new(parts.get(i)));
            i = i + 1;
        }

        return result;
    }

    fun repeat(count: num): FluentString {
        return FluentString::new(this.value.repeat(count));
    }

    fun pad_left(width: num, padding: string): FluentString {
        return FluentString::new(this.value.pad_left(width, padding));
    }

    fun pad_right(width: num, padding: string): FluentString {
        return FluentString::new(this.value.pad_right(width, padding));
    }

    fun reverse(): FluentString {
        let result = "";
        let i = this.value.length() - 1;

        while i >= 0 {
            result = result + this.value.char_at(i);
            i = i - 1;
        }

        return FluentString::new(result);
    }

    fun count_chars(): num {
        return this.value.length();
    }

    fun count_words(): num {
        let trimmed = this.trim();
        if trimmed.is_empty() {
            return 0;
        }

        let words = trimmed.split(" ");
        let count = 0;

        let i = 0;
        while i < words.length() {
            if !words.get(i).is_empty() {
                count = count + 1;
            }
            i = i + 1;
        }

        return count;
    }

    fun count_lines(): num {
        if this.is_empty() {
            return 0;
        }

        let lines = this.split("\n");
        return lines.length();
    }

    fun is_numeric(): bool {
        if this.is_empty() {
            return false;
        }

        let iter = this.iterator();
        while iter.has_next() {
            let ch = iter.next();
            if !CharUtils::is_digit(ch) && ch != "." && ch != "-" && ch != "+" {
                return false;
            }
        }

        return true;
    }

    fun is_alphabetic(): bool {
        if this.is_empty() {
            return false;
        }

        let iter = this.iterator();
        while iter.has_next() {
            let ch = iter.next();
            if !CharUtils::is_letter(ch) {
                return false;
            }
        }

        return true;
    }

    fun is_alphanumeric(): bool {
        if this.is_empty() {
            return false;
        }

        let iter = this.iterator();
        while iter.has_next() {
            let ch = iter.next();
            if !CharUtils::is_alphanumeric(ch) {
                return false;
            }
        }

        return true;
    }

    fun compare_to(other: FluentString): StringComparison {
        if this.value == other.value {
            return StringComparison::Equal;
        } else if this.value < other.value {
            return StringComparison::Less;
        } else {
            return StringComparison::Greater;
        }
    }

    fun equals_ignore_case(other: FluentString): bool {
        return this.to_lower_case().value == other.to_lower_case().value;
    }

    fun to_string(): string {
        return this.value;
    }
}

// Implement Iterator for StringIterator
StringIterator: Iterator<string> {
    fun new(text: string): StringIterator {
        return StringIterator { text: text, index: 0 };
    }

    fun has_next(): bool {
        return this.index < this.text.length();
    }

    fun next(): string {
        if !this.has_next() {
            panic("No more characters");
        }

        let ch = this.text.char_at(this.index);
        this.index = this.index + 1;
        return ch;
    }

    fun reset(): void {
        this.index = 0;
    }
}

// StringBuilder implementation for efficient string building
StringBuilder {
    fun new(): StringBuilder {
        let parts = Array<string>();
        return StringBuilder { parts: parts };
    }

    fun append(text: string): StringBuilder {
        this.parts.add(text);
        return this;
    }

    fun append_char(ch: string): StringBuilder {
        this.parts.add(ch);
        return this;
    }

    fun append_line(text: string): StringBuilder {
        this.parts.add(text);
        this.parts.add("\n");
        return this;
    }

    fun append_number(num: num): StringBuilder {
        this.parts.add(num.to_string());
        return this;
    }

    fun append_bool(value: bool): StringBuilder {
        this.parts.add(value.to_string());
        return this;
    }

    fun insert(index: num, text: string): StringBuilder {
        // For simplicity, we'll rebuild the array
        let newParts = Array<string>();
        let i = 0;

        while i < this.parts.length() {
            if i == index {
                newParts.add(text);
            }
            newParts.add(this.parts.get(i));
            i = i + 1;
        }

        if index >= this.parts.length() {
            newParts.add(text);
        }

        this.parts = newParts;
        return this;
    }

    fun clear(): StringBuilder {
        this.parts.clear();
        return this;
    }

    fun length(): num {
        let total = 0;
        let i = 0;

        while i < this.parts.length() {
            total = total + this.parts.get(i).length();
            i = i + 1;
        }

        return total;
    }

    fun is_empty(): bool {
        return this.parts.length() == 0;
    }

    fun to_string(): string {
        let result = "";
        let i = 0;

        while i < this.parts.length() {
            result = result + this.parts.get(i);
            i = i + 1;
        }

        return result;
    }

    fun to_fluent_string(): FluentString {
        return FluentString::new(this.to_string());
    }
}

export { FluentString, StringIterator, StringBuilder, StringComparison, CaseStyle };
