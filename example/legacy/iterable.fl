// Iterable interface and utilities for Fluent Lang

interface Iterable<T> {
    fun iterator(): Iterator<T>;
    fun length(): num;
    fun is_empty(): bool;
    fun is_not_empty(): bool;
}

interface Iterator<T> {
    fun has_next(): bool;
    fun next(): T;
    fun reset(): void;
}

interface Collection<T> {
    fun add(item: T): void;
    fun remove(item: T): bool;
    fun contains(item: T): bool;
    fun clear(): void;
    fun to_array(): Array<T>;
}

// Iterable utilities and helper functions
struct IterableUtils {
    // Static utility methods
}

IterableUtils {
    static fun map<T, R>(iterable: Iterable<T>, mapper: fun(T): R): Array<R> {
        let result = Array<R>();
        let iter = iterable.iterator();
        
        while iter.has_next() {
            let item = iter.next();
            let mapped = mapper(item);
            result.add(mapped);
        }
        
        return result;
    }
    
    static fun filter<T>(iterable: Iterable<T>, predicate: fun(T): bool): Array<T> {
        let result = Array<T>();
        let iter = iterable.iterator();
        
        while iter.has_next() {
            let item = iter.next();
            if predicate(item) {
                result.add(item);
            }
        }
        
        return result;
    }
    
    static fun reduce<T, R>(iterable: Iterable<T>, initial: R, reducer: fun(R, T): R): R {
        let result = initial;
        let iter = iterable.iterator();
        
        while iter.has_next() {
            let item = iter.next();
            result = reducer(result, item);
        }
        
        return result;
    }
    
    static fun for_each<T>(iterable: Iterable<T>, action: fun(T): void): void {
        let iter = iterable.iterator();
        
        while iter.has_next() {
            let item = iter.next();
            action(item);
        }
    }
    
    static fun find<T>(iterable: Iterable<T>, predicate: fun(T): bool): T? {
        let iter = iterable.iterator();
        
        while iter.has_next() {
            let item = iter.next();
            if predicate(item) {
                return item;
            }
        }
        
        return null;
    }
    
    static fun any<T>(iterable: Iterable<T>, predicate: fun(T): bool): bool {
        let iter = iterable.iterator();
        
        while iter.has_next() {
            let item = iter.next();
            if predicate(item) {
                return true;
            }
        }
        
        return false;
    }
    
    static fun all<T>(iterable: Iterable<T>, predicate: fun(T): bool): bool {
        let iter = iterable.iterator();
        
        while iter.has_next() {
            let item = iter.next();
            if !predicate(item) {
                return false;
            }
        }
        
        return true;
    }
    
    static fun count<T>(iterable: Iterable<T>, predicate: fun(T): bool): num {
        let count = 0;
        let iter = iterable.iterator();
        
        while iter.has_next() {
            let item = iter.next();
            if predicate(item) {
                count = count + 1;
            }
        }
        
        return count;
    }
    
    static fun take<T>(iterable: Iterable<T>, count: num): Array<T> {
        let result = Array<T>();
        let iter = iterable.iterator();
        let taken = 0;
        
        while iter.has_next() && taken < count {
            result.add(iter.next());
            taken = taken + 1;
        }
        
        return result;
    }
    
    static fun skip<T>(iterable: Iterable<T>, count: num): Array<T> {
        let result = Array<T>();
        let iter = iterable.iterator();
        let skipped = 0;
        
        // Skip first 'count' items
        while iter.has_next() && skipped < count {
            iter.next();
            skipped = skipped + 1;
        }
        
        // Take remaining items
        while iter.has_next() {
            result.add(iter.next());
        }
        
        return result;
    }
    
    static fun concat<T>(first: Iterable<T>, second: Iterable<T>): Array<T> {
        let result = Array<T>();
        
        // Add all items from first iterable
        let iter1 = first.iterator();
        while iter1.has_next() {
            result.add(iter1.next());
        }
        
        // Add all items from second iterable
        let iter2 = second.iterator();
        while iter2.has_next() {
            result.add(iter2.next());
        }
        
        return result;
    }
    
    static fun to_string<T>(iterable: Iterable<T>, separator: string): string {
        let result = "";
        let iter = iterable.iterator();
        let first = true;
        
        while iter.has_next() {
            if !first {
                result = result + separator;
            }
            
            let item = iter.next();
            result = result + item.to_string();
            first = false;
        }
        
        return result;
    }
}

export { Iterable, Iterator, Collection, IterableUtils };
