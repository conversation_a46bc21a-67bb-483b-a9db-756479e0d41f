// Advanced String utilities and algorithms for Fluent Lang

import "string_lib.fl" show <PERSON>luent<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, StringCom<PERSON>ison, CaseStyle;
import "char.fl" show Char<PERSON>til<PERSON>, CharType;
import "iterable.fl" show IterableUtils;

// String utility functions
struct StringUtils {
    // Static utility methods
}

StringUtils {
    // Case conversion utilities
    static fun to_camel_case(text: string): string {
        let words = FluentString::new(text).split(" ");
        let builder = StringBuilder::new();
        
        let i = 0;
        while i < words.length() {
            let word = words.get(i);
            if i == 0 {
                builder.append(word.to_lower_case().to_string());
            } else {
                let lower = word.to_lower_case();
                if !lower.is_empty() {
                    let first = CharUtils::to_upper_case(lower.char_at(0));
                    let rest = lower.substring(1, lower.length()).to_string();
                    builder.append(first + rest);
                }
            }
            i = i + 1;
        }
        
        return builder.to_string();
    }
    
    static fun to_pascal_case(text: string): string {
        let words = FluentString::new(text).split(" ");
        let builder = StringBuilder::new();
        
        let i = 0;
        while i < words.length() {
            let word = words.get(i).to_lower_case();
            if !word.is_empty() {
                let first = CharUtils::to_upper_case(word.char_at(0));
                let rest = word.substring(1, word.length()).to_string();
                builder.append(first + rest);
            }
            i = i + 1;
        }
        
        return builder.to_string();
    }
    
    static fun to_snake_case(text: string): string {
        let result = "";
        let i = 0;
        
        while i < text.length() {
            let ch = text.char_at(i);
            
            if CharUtils::is_upper_case(ch) && i > 0 {
                result = result + "_";
            }
            
            result = result + CharUtils::to_lower_case(ch);
            i = i + 1;
        }
        
        return result;
    }
    
    static fun to_kebab_case(text: string): string {
        let snake = StringUtils::to_snake_case(text);
        return snake.replace("_", "-");
    }
    
    static fun to_title_case(text: string): string {
        let words = FluentString::new(text).split(" ");
        let builder = StringBuilder::new();
        
        let i = 0;
        while i < words.length() {
            let word = words.get(i).to_lower_case();
            if !word.is_empty() {
                let first = CharUtils::to_upper_case(word.char_at(0));
                let rest = word.substring(1, word.length()).to_string();
                builder.append(first + rest);
                
                if i < words.length() - 1 {
                    builder.append(" ");
                }
            }
            i = i + 1;
        }
        
        return builder.to_string();
    }
    
    // String validation utilities
    static fun is_valid_email(email: string): bool {
        let fs = FluentString::new(email);
        
        if !fs.contains("@") {
            return false;
        }
        
        let parts = fs.split("@");
        if parts.length() != 2 {
            return false;
        }
        
        let local = parts.get(0);
        let domain = parts.get(1);
        
        if local.is_empty() || domain.is_empty() {
            return false;
        }
        
        if !domain.contains(".") {
            return false;
        }
        
        return true;
    }
    
    static fun is_valid_url(url: string): bool {
        let fs = FluentString::new(url);
        
        return fs.starts_with("http://") || fs.starts_with("https://") || fs.starts_with("ftp://");
    }
    
    static fun is_valid_phone(phone: string): bool {
        let fs = FluentString::new(phone);
        let cleaned = fs.replace("-", "").replace(" ", "").replace("(", "").replace(")", "");
        
        if cleaned.length() < 10 {
            return false;
        }
        
        return cleaned.is_numeric();
    }
    
    // String manipulation utilities
    static fun reverse_words(text: string): string {
        let words = FluentString::new(text).split(" ");
        let builder = StringBuilder::new();
        
        let i = words.length() - 1;
        while i >= 0 {
            builder.append(words.get(i).to_string());
            if i > 0 {
                builder.append(" ");
            }
            i = i - 1;
        }
        
        return builder.to_string();
    }
    
    static fun remove_duplicates(text: string): string {
        let result = "";
        let seen = Array<string>();
        
        let i = 0;
        while i < text.length() {
            let ch = text.char_at(i);
            
            let found = false;
            let j = 0;
            while j < seen.length() {
                if seen.get(j) == ch {
                    found = true;
                    break;
                }
                j = j + 1;
            }
            
            if !found {
                seen.add(ch);
                result = result + ch;
            }
            
            i = i + 1;
        }
        
        return result;
    }
    
    static fun compress_whitespace(text: string): string {
        let result = "";
        let prev_was_space = false;
        
        let i = 0;
        while i < text.length() {
            let ch = text.char_at(i);
            
            if CharUtils::is_whitespace(ch) {
                if !prev_was_space {
                    result = result + " ";
                    prev_was_space = true;
                }
            } else {
                result = result + ch;
                prev_was_space = false;
            }
            
            i = i + 1;
        }
        
        return result;
    }
    
    static fun extract_numbers(text: string): Array<string> {
        let numbers = Array<string>();
        let current_number = "";
        
        let i = 0;
        while i < text.length() {
            let ch = text.char_at(i);
            
            if CharUtils::is_digit(ch) || ch == "." {
                current_number = current_number + ch;
            } else {
                if !current_number.is_empty() {
                    numbers.add(current_number);
                    current_number = "";
                }
            }
            
            i = i + 1;
        }
        
        if !current_number.is_empty() {
            numbers.add(current_number);
        }
        
        return numbers;
    }
    
    static fun extract_words(text: string): Array<string> {
        let words = Array<string>();
        let current_word = "";
        
        let i = 0;
        while i < text.length() {
            let ch = text.char_at(i);
            
            if CharUtils::is_alphanumeric(ch) {
                current_word = current_word + ch;
            } else {
                if !current_word.is_empty() {
                    words.add(current_word);
                    current_word = "";
                }
            }
            
            i = i + 1;
        }
        
        if !current_word.is_empty() {
            words.add(current_word);
        }
        
        return words;
    }
    
    // String comparison utilities
    static fun levenshtein_distance(str1: string, str2: string): num {
        let len1 = str1.length();
        let len2 = str2.length();
        
        if len1 == 0 { return len2; }
        if len2 == 0 { return len1; }
        
        // For simplicity, we'll use a basic implementation
        // In a real implementation, we'd use dynamic programming
        if str1 == str2 { return 0; }
        
        let changes = 0;
        let i = 0;
        let min_len = len1;
        if len2 < min_len { min_len = len2; }
        
        while i < min_len {
            if str1.char_at(i) != str2.char_at(i) {
                changes = changes + 1;
            }
            i = i + 1;
        }
        
        changes = changes + (len1 - len2);
        if changes < 0 { changes = -changes; }
        
        return changes;
    }
    
    static fun similarity_percentage(str1: string, str2: string): num {
        let distance = StringUtils::levenshtein_distance(str1, str2);
        let max_len = str1.length();
        if str2.length() > max_len { max_len = str2.length(); }
        
        if max_len == 0 { return 100; }
        
        let similarity = (max_len - distance) * 100 / max_len;
        return similarity;
    }
    
    // String formatting utilities
    static fun format_with_args(template: string, args: Array<string>): string {
        let result = template;
        
        let i = 0;
        while i < args.length() {
            let placeholder = "{" + i.to_string() + "}";
            result = result.replace(placeholder, args.get(i));
            i = i + 1;
        }
        
        return result;
    }
    
    static fun join_with_separator(strings: Array<string>, separator: string): string {
        if strings.length() == 0 {
            return "";
        }
        
        let builder = StringBuilder::new();
        let i = 0;
        
        while i < strings.length() {
            builder.append(strings.get(i));
            if i < strings.length() - 1 {
                builder.append(separator);
            }
            i = i + 1;
        }
        
        return builder.to_string();
    }
}

export { StringUtils };
