// Demo of Char and String libraries for Fluent Lang

import "char.fl" show Char<PERSON><PERSON><PERSON>, CharType, CharConstants;
import "string_lib.fl" show FluentString, <PERSON><PERSON><PERSON><PERSON>, StringComparison;
import "string_advanced.fl" show StringUtils;
import "iterable.fl" show IterableUtils;

fun demo_char_utilities(): void {
    print("=== Character Utilities Demo ===");
    
    let ch = "A";
    print("Character: " + ch);
    print("Is letter: " + CharUtils::is_letter(ch).to_string());
    print("Is digit: " + CharUtils::is_digit(ch).to_string());
    print("Is upper case: " + CharUtils::is_upper_case(ch).to_string());
    print("Is lower case: " + CharUtils::is_lower_case(ch).to_string());
    print("To lower case: " + CharUtils::to_lower_case(ch));
    
    let digit = "5";
    print("Digit: " + digit);
    print("Is digit: " + CharUtils::is_digit(digit).to_string());
    print("Digit to number: " + CharUtils::digit_to_number(digit).to_string());
    
    let vowel = "e";
    print("Character: " + vowel);
    print("Is vowel: " + CharUtils::is_vowel(vowel).to_string());
    print("Is consonant: " + CharUtils::is_consonant(vowel).to_string());
    
    print("Space character: '" + CharConstants::space() + "'");
    print("Tab character: '" + CharConstants::tab() + "'");
}

fun demo_fluent_string(): void {
    print("=== FluentString Demo ===");
    
    let fs = FluentString::new("Hello, Fluent Lang!");
    print("Original: " + fs.to_string());
    print("Length: " + fs.length().to_string());
    print("Is empty: " + fs.is_empty().to_string());
    print("Upper case: " + fs.to_upper_case().to_string());
    print("Lower case: " + fs.to_lower_case().to_string());
    
    print("Contains 'Fluent': " + fs.contains("Fluent").to_string());
    print("Starts with 'Hello': " + fs.starts_with("Hello").to_string());
    print("Ends with 'Lang!': " + fs.ends_with("Lang!").to_string());
    
    print("Index of 'Fluent': " + fs.index_of("Fluent").to_string());
    print("Substring (0, 5): " + fs.substring(0, 5).to_string());
    
    let reversed = fs.reverse();
    print("Reversed: " + reversed.to_string());
    
    print("Word count: " + fs.count_words().to_string());
    print("Character count: " + fs.count_chars().to_string());
}

fun demo_string_iteration(): void {
    print("=== String Iteration Demo ===");
    
    let fs = FluentString::new("Hello");
    print("Iterating through characters:");
    
    let iter = fs.iterator();
    while iter.has_next() {
        let ch = iter.next();
        print("  Character: " + ch + " (Type: " + CharUtils::get_char_type(ch).to_string() + ")");
    }
    
    // Using iterable utilities
    print("Using IterableUtils:");
    let chars = Array<string>();
    let iter2 = fs.iterator();
    while iter2.has_next() {
        chars.add(iter2.next());
    }
    
    // Note: In a real implementation, we'd use IterableUtils::map
    print("Characters collected: " + chars.to_string());
}

fun demo_string_builder(): void {
    print("=== StringBuilder Demo ===");
    
    let builder = StringBuilder::new();
    builder.append("Hello")
           .append(" ")
           .append("World")
           .append_char("!")
           .append_line("")
           .append("This is line 2")
           .append_number(42)
           .append(" is the answer");
    
    print("Built string:");
    print(builder.to_string());
    print("Length: " + builder.length().to_string());
}

fun demo_string_validation(): void {
    print("=== String Validation Demo ===");
    
    let email = "<EMAIL>";
    print("Email: " + email);
    print("Is valid email: " + StringUtils::is_valid_email(email).to_string());
    
    let url = "https://www.example.com";
    print("URL: " + url);
    print("Is valid URL: " + StringUtils::is_valid_url(url).to_string());
    
    let phone = "(*************";
    print("Phone: " + phone);
    print("Is valid phone: " + StringUtils::is_valid_phone(phone).to_string());
    
    let text = "Hello123";
    let fs = FluentString::new(text);
    print("Text: " + text);
    print("Is numeric: " + fs.is_numeric().to_string());
    print("Is alphabetic: " + fs.is_alphabetic().to_string());
    print("Is alphanumeric: " + fs.is_alphanumeric().to_string());
}

fun demo_case_conversion(): void {
    print("=== Case Conversion Demo ===");
    
    let text = "hello world example";
    print("Original: " + text);
    print("Camel case: " + StringUtils::to_camel_case(text));
    print("Pascal case: " + StringUtils::to_pascal_case(text));
    print("Snake case: " + StringUtils::to_snake_case(text));
    print("Kebab case: " + StringUtils::to_kebab_case(text));
    print("Title case: " + StringUtils::to_title_case(text));
}

fun demo_string_manipulation(): void {
    print("=== String Manipulation Demo ===");
    
    let text = "The quick brown fox";
    print("Original: " + text);
    print("Reverse words: " + StringUtils::reverse_words(text));
    
    let duplicates = "aabbccddee";
    print("With duplicates: " + duplicates);
    print("Remove duplicates: " + StringUtils::remove_duplicates(duplicates));
    
    let whitespace = "  Hello    world   with   spaces  ";
    print("With whitespace: '" + whitespace + "'");
    print("Compressed: '" + StringUtils::compress_whitespace(whitespace) + "'");
    
    let mixed = "Hello123World456Test789";
    print("Mixed text: " + mixed);
    let numbers = StringUtils::extract_numbers(mixed);
    print("Extracted numbers: " + numbers.to_string());
    let words = StringUtils::extract_words(mixed);
    print("Extracted words: " + words.to_string());
}

fun demo_string_comparison(): void {
    print("=== String Comparison Demo ===");
    
    let str1 = "hello";
    let str2 = "world";
    let str3 = "hello";
    
    let fs1 = FluentString::new(str1);
    let fs2 = FluentString::new(str2);
    let fs3 = FluentString::new(str3);
    
    print("String 1: " + str1);
    print("String 2: " + str2);
    print("String 3: " + str3);
    
    print("str1 compare to str2: " + fs1.compare_to(fs2).to_string());
    print("str1 compare to str3: " + fs1.compare_to(fs3).to_string());
    
    let distance = StringUtils::levenshtein_distance(str1, str2);
    print("Levenshtein distance between '" + str1 + "' and '" + str2 + "': " + distance.to_string());
    
    let similarity = StringUtils::similarity_percentage(str1, str2);
    print("Similarity percentage: " + similarity.to_string() + "%");
}

fun demo_string_formatting(): void {
    print("=== String Formatting Demo ===");
    
    let template = "Hello {0}, welcome to {1}! You have {2} messages.";
    let args = Array<string>();
    args.add("John");
    args.add("Fluent Lang");
    args.add("5");
    
    let formatted = StringUtils::format_with_args(template, args);
    print("Template: " + template);
    print("Formatted: " + formatted);
    
    let strings = Array<string>();
    strings.add("apple");
    strings.add("banana");
    strings.add("cherry");
    
    let joined = StringUtils::join_with_separator(strings, ", ");
    print("Joined strings: " + joined);
}

fun main(): void {
    print("=== Fluent Lang Char and String Libraries Demo ===");
    
    demo_char_utilities();
    print("");
    demo_fluent_string();
    print("");
    demo_string_iteration();
    print("");
    demo_string_builder();
    print("");
    demo_string_validation();
    print("");
    demo_case_conversion();
    print("");
    demo_string_manipulation();
    print("");
    demo_string_comparison();
    print("");
    demo_string_formatting();
    
    print("=== Demo Complete ===");
}

main();
