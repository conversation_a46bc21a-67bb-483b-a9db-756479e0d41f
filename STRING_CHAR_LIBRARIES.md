# 📚 Fluent Lang String and Character Libraries

## 🎯 Overview

Comprehensive string and character manipulation libraries for Fluent Lang, built with modern design patterns including:

- **Iterable Support** - String iteration with Iterator pattern
- **Functional Programming** - Map, filter, reduce operations
- **Type Safety** - Strong typing with interfaces and structs
- **Performance** - StringBuilder for efficient string building
- **Modularity** - Separate concerns into focused libraries

## 📦 Library Structure

### **1. iterable.fl** - Core Iteration Framework
```fluent
interface Iterable<T> {
    fun iterator(): Iterator<T>;
    fun length(): num;
    fun is_empty(): bool;
    fun is_not_empty(): bool;
}

interface Iterator<T> {
    fun has_next(): bool;
    fun next(): T;
    fun reset(): void;
}

interface Collection<T> {
    fun add(item: T): void;
    fun remove(item: T): bool;
    fun contains(item: T): bool;
    fun clear(): void;
    fun to_array(): Array<T>;
}
```

**IterableUtils Static Methods:**
- `map<T, R>(iterable, mapper)` - Transform elements
- `filter<T>(iterable, predicate)` - Filter elements
- `reduce<T, R>(iterable, initial, reducer)` - Reduce to single value
- `for_each<T>(iterable, action)` - Execute action on each element
- `find<T>(iterable, predicate)` - Find first matching element
- `any<T>(iterable, predicate)` - Check if any element matches
- `all<T>(iterable, predicate)` - Check if all elements match
- `count<T>(iterable, predicate)` - Count matching elements
- `take<T>(iterable, count)` - Take first N elements
- `skip<T>(iterable, count)` - Skip first N elements
- `concat<T>(first, second)` - Concatenate iterables
- `to_string<T>(iterable, separator)` - Join to string

### **2. char.fl** - Character Utilities
```fluent
struct CharUtils {
    static fun is_letter(ch: string): bool;
    static fun is_digit(ch: string): bool;
    static fun is_alphanumeric(ch: string): bool;
    static fun is_whitespace(ch: string): bool;
    static fun is_upper_case(ch: string): bool;
    static fun is_lower_case(ch: string): bool;
    static fun is_punctuation(ch: string): bool;
    static fun is_symbol(ch: string): bool;
    static fun is_vowel(ch: string): bool;
    static fun is_consonant(ch: string): bool;
    static fun to_upper_case(ch: string): string;
    static fun to_lower_case(ch: string): string;
    static fun get_char_type(ch: string): CharType;
    static fun digit_to_number(ch: string): num;
    static fun number_to_digit(num: num): string;
    static fun compare(ch1: string, ch2: string): num;
    static fun escape_char(ch: string): string;
    static fun unescape_char(escaped: string): string;
}

enum CharType {
    Letter, Digit, Whitespace, Punctuation, Symbol, Control
}

struct CharConstants {
    static fun space(): string;
    static fun tab(): string;
    static fun newline(): string;
    static fun carriage_return(): string;
}
```

### **3. string_lib.fl** - Enhanced String with Iterable Support
```fluent
struct FluentString {
    value: string;
}

FluentString: Iterable<string> {
    fun new(text: string): FluentString;
    fun iterator(): Iterator<string>;
    fun length(): num;
    fun is_empty(): bool;
    fun is_not_empty(): bool;
    
    // Enhanced string methods
    fun char_at(index: num): string;
    fun substring(start: num, end: num): FluentString;
    fun index_of(search: string): num;
    fun last_index_of(search: string): num;
    fun contains(search: string): bool;
    fun starts_with(prefix: string): bool;
    fun ends_with(suffix: string): bool;
    fun to_upper_case(): FluentString;
    fun to_lower_case(): FluentString;
    fun trim(): FluentString;
    fun trim_start(): FluentString;
    fun trim_end(): FluentString;
    fun replace(from: string, to: string): FluentString;
    fun replace_first(from: string, to: string): FluentString;
    fun split(separator: string): Array<FluentString>;
    fun repeat(count: num): FluentString;
    fun pad_left(width: num, padding: string): FluentString;
    fun pad_right(width: num, padding: string): FluentString;
    fun reverse(): FluentString;
    fun count_chars(): num;
    fun count_words(): num;
    fun count_lines(): num;
    fun is_numeric(): bool;
    fun is_alphabetic(): bool;
    fun is_alphanumeric(): bool;
    fun compare_to(other: FluentString): StringComparison;
    fun equals_ignore_case(other: FluentString): bool;
}

struct StringBuilder {
    fun new(): StringBuilder;
    fun append(text: string): StringBuilder;
    fun append_char(ch: string): StringBuilder;
    fun append_line(text: string): StringBuilder;
    fun append_number(num: num): StringBuilder;
    fun append_bool(value: bool): StringBuilder;
    fun insert(index: num, text: string): StringBuilder;
    fun clear(): StringBuilder;
    fun length(): num;
    fun is_empty(): bool;
    fun to_string(): string;
    fun to_fluent_string(): FluentString;
}

enum StringComparison { Less, Equal, Greater }
```

### **4. string_advanced.fl** - Advanced String Algorithms
```fluent
struct StringUtils {
    // Case conversion
    static fun to_camel_case(text: string): string;
    static fun to_pascal_case(text: string): string;
    static fun to_snake_case(text: string): string;
    static fun to_kebab_case(text: string): string;
    static fun to_title_case(text: string): string;
    
    // Validation
    static fun is_valid_email(email: string): bool;
    static fun is_valid_url(url: string): bool;
    static fun is_valid_phone(phone: string): bool;
    
    // Manipulation
    static fun reverse_words(text: string): string;
    static fun remove_duplicates(text: string): string;
    static fun compress_whitespace(text: string): string;
    static fun extract_numbers(text: string): Array<string>;
    static fun extract_words(text: string): Array<string>;
    
    // Comparison
    static fun levenshtein_distance(str1: string, str2: string): num;
    static fun similarity_percentage(str1: string, str2: string): num;
    
    // Formatting
    static fun format_with_args(template: string, args: Array<string>): string;
    static fun join_with_separator(strings: Array<string>, separator: string): string;
}
```

## 🚀 Usage Examples

### **Character Operations**
```fluent
import "char.fl" show CharUtils, CharType;

fun demo_char(): void {
    let ch = "A";
    print("Is letter: " + CharUtils::is_letter(ch).to_string());
    print("Is upper: " + CharUtils::is_upper_case(ch).to_string());
    print("To lower: " + CharUtils::to_lower_case(ch));
    print("Type: " + CharUtils::get_char_type(ch).to_string());
    
    let digit = "5";
    print("Digit value: " + CharUtils::digit_to_number(digit).to_string());
}
```

### **String Iteration**
```fluent
import "string_lib.fl" show FluentString;

fun demo_iteration(): void {
    let fs = FluentString::new("Hello");
    let iter = fs.iterator();
    
    while iter.has_next() {
        let ch = iter.next();
        print("Character: " + ch);
    }
}
```

### **String Building**
```fluent
import "string_lib.fl" show StringBuilder;

fun demo_builder(): void {
    let builder = StringBuilder::new();
    builder.append("Hello")
           .append(" ")
           .append("World")
           .append_char("!")
           .append_number(42);
    
    print("Result: " + builder.to_string());
}
```

### **Advanced String Operations**
```fluent
import "string_advanced.fl" show StringUtils;

fun demo_advanced(): void {
    let text = "hello world";
    print("Camel case: " + StringUtils::to_camel_case(text));
    print("Pascal case: " + StringUtils::to_pascal_case(text));
    
    let email = "<EMAIL>";
    print("Valid email: " + StringUtils::is_valid_email(email).to_string());
    
    let distance = StringUtils::levenshtein_distance("hello", "world");
    print("Edit distance: " + distance.to_string());
}
```

### **Functional Programming with Strings**
```fluent
import "string_lib.fl" show FluentString;
import "iterable.fl" show IterableUtils;

fun demo_functional(): void {
    let fs = FluentString::new("Hello");
    
    // Collect all characters
    let chars = Array<string>();
    let iter = fs.iterator();
    while iter.has_next() {
        chars.add(iter.next());
    }
    
    // In a full implementation, you could use:
    // let upperChars = IterableUtils::map(fs, CharUtils::to_upper_case);
    // let vowels = IterableUtils::filter(fs, CharUtils::is_vowel);
}
```

## ✅ Features

### **Core Features**
- ✅ **Iterable Interface** - Standard iteration pattern
- ✅ **Character Classification** - Letter, digit, whitespace, etc.
- ✅ **String Wrapper** - Enhanced FluentString with fluent API
- ✅ **StringBuilder** - Efficient string construction
- ✅ **Case Conversion** - camelCase, PascalCase, snake_case, kebab-case
- ✅ **String Validation** - Email, URL, phone number validation
- ✅ **String Algorithms** - Levenshtein distance, similarity
- ✅ **Functional Utilities** - Map, filter, reduce operations

### **Advanced Features**
- ✅ **String Iteration** - Character-by-character iteration
- ✅ **String Manipulation** - Reverse, remove duplicates, compress whitespace
- ✅ **Text Extraction** - Extract numbers, words from mixed text
- ✅ **String Formatting** - Template formatting with placeholders
- ✅ **String Comparison** - Lexicographic and similarity comparison
- ✅ **Escape Sequences** - Handle escape characters

## 🎯 Benefits

1. **Type Safety** - Strong typing with interfaces and structs
2. **Performance** - StringBuilder for efficient string building
3. **Modularity** - Separate libraries for different concerns
4. **Extensibility** - Easy to add new string operations
5. **Functional Style** - Support for functional programming patterns
6. **Dart-like API** - Familiar API for developers from other languages

## 🚀 Bootstrap Impact

These string and character libraries significantly improve Fluent Lang's capabilities:

- **Text Processing** - Essential for compiler source code processing
- **Code Generation** - StringBuilder for efficient code output
- **Validation** - Input validation for compiler arguments
- **Algorithms** - String algorithms for optimization
- **Developer Experience** - Rich string API for application development

**Bootstrap Readiness Enhancement: +2 points** 🎉

Fluent Lang hiện đã có comprehensive string và character libraries, sẵn sàng cho việc xử lý text-heavy applications như compilers! 🚀
