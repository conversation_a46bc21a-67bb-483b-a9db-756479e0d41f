import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Test String and Char Libraries ===');
  
  testIterableLibrary();
  testCharLibrary();
  testStringLibrary();
  testStringAdvanced();
  testStringCharDemo();
}

void testIterableLibrary() {
  print('\n--- Test 1: Iterable Library ---');
  
  final code = '''
import "iterable.fl" show Iterable, Iterator, Collection, IterableUtils;

fun main(): void {
  print("Iterable library loaded successfully");
}

main();
''';

  runTest(code, 'Iterable Library');
}

void testCharLibrary() {
  print('\n--- Test 2: Char Library ---');
  
  final code = '''
import "char.fl" show CharUtils, CharType, CharConstants;

fun main(): void {
  print("Char library loaded successfully");
  
  let ch = "A";
  print("Character: " + ch);
  print("Is letter: " + CharUtils::is_letter(ch).to_string());
  print("Is upper case: " + CharUtils::is_upper_case(ch).to_string());
  print("To lower case: " + CharUtils::to_lower_case(ch));
}

main();
''';

  runTest(code, 'Char Library');
}

void testStringLibrary() {
  print('\n--- Test 3: String Library ---');
  
  final code = '''
import "string_lib.fl" show FluentString, StringBuilder, StringComparison;

fun main(): void {
  print("String library loaded successfully");
  
  let fs = FluentString::new("Hello World");
  print("FluentString: " + fs.to_string());
  print("Length: " + fs.length().to_string());
  print("Upper case: " + fs.to_upper_case().to_string());
  
  let builder = StringBuilder::new();
  builder.append("Hello").append(" ").append("World");
  print("StringBuilder result: " + builder.to_string());
}

main();
''';

  runTest(code, 'String Library');
}

void testStringAdvanced() {
  print('\n--- Test 4: String Advanced Library ---');
  
  final code = '''
import "string_advanced.fl" show StringUtils;

fun main(): void {
  print("String advanced library loaded successfully");
  
  let text = "hello world";
  print("Original: " + text);
  print("Camel case: " + StringUtils::to_camel_case(text));
  print("Pascal case: " + StringUtils::to_pascal_case(text));
  
  let email = "<EMAIL>";
  print("Email: " + email);
  print("Is valid: " + StringUtils::is_valid_email(email).to_string());
}

main();
''';

  runTest(code, 'String Advanced Library');
}

void testStringCharDemo() {
  print('\n--- Test 5: String Char Demo (Parsing Only) ---');
  
  final code = '''
import "char.fl" show CharUtils, CharType;
import "string_lib.fl" show FluentString, StringBuilder;
import "string_advanced.fl" show StringUtils;

fun demo_basic(): void {
    let ch = "A";
    print("Character: " + ch);
    print("Is letter: " + CharUtils::is_letter(ch).to_string());
    
    let fs = FluentString::new("Hello");
    print("String: " + fs.to_string());
    print("Length: " + fs.length().to_string());
    
    let builder = StringBuilder::new();
    builder.append("Test");
    print("Builder: " + builder.to_string());
    
    let camel = StringUtils::to_camel_case("hello world");
    print("Camel case: " + camel);
}

fun main(): void {
    print("Demo parsing test");
    demo_basic();
}

main();
''';

  runTest(code, 'String Char Demo');
}

void testIterableUtilities() {
  print('\n--- Test 6: Iterable Utilities ---');
  
  final code = '''
import "iterable.fl" show IterableUtils;
import "string_lib.fl" show FluentString;

fun main(): void {
  print("Testing iterable utilities");
  
  let fs = FluentString::new("Hello");
  print("String: " + fs.to_string());
  print("Is iterable: " + fs.is_not_empty().to_string());
  
  let iter = fs.iterator();
  print("Has characters: " + iter.has_next().to_string());
}

main();
''';

  runTest(code, 'Iterable Utilities');
}

void testStringValidation() {
  print('\n--- Test 7: String Validation ---');
  
  final code = '''
import "string_advanced.fl" show StringUtils;
import "string_lib.fl" show FluentString;

fun main(): void {
  print("Testing string validation");
  
  let email = "<EMAIL>";
  print("Email validation: " + StringUtils::is_valid_email(email).to_string());
  
  let url = "https://example.com";
  print("URL validation: " + StringUtils::is_valid_url(url).to_string());
  
  let fs = FluentString::new("123");
  print("Is numeric: " + fs.is_numeric().to_string());
}

main();
''';

  runTest(code, 'String Validation');
}

void testStringManipulation() {
  print('\n--- Test 8: String Manipulation ---');
  
  final code = '''
import "string_advanced.fl" show StringUtils;
import "string_lib.fl" show FluentString;

fun main(): void {
  print("Testing string manipulation");
  
  let text = "hello world";
  print("Original: " + text);
  print("Reversed words: " + StringUtils::reverse_words(text));
  
  let fs = FluentString::new(text);
  print("Reversed string: " + fs.reverse().to_string());
  
  let duplicates = "aabbcc";
  print("Remove duplicates: " + StringUtils::remove_duplicates(duplicates));
}

main();
''';

  runTest(code, 'String Manipulation');
}

void runTest(String code, String testName) {
  try {
    final parser = buildParser(verbose: false);
    final ast = parser.parse(code);
    
    if (ast.isFailure) {
      print('❌ $testName - Parse Error: ${ast.message}');
      return;
    }
    
    final compiler = Compiler();
    final bytecode = compiler.compile(ast.value);
    
    final vm = VirtualMachine();
    vm.load(bytecode);
    vm.run();
    
    print('✅ $testName - Success');
  } catch (e) {
    if (e.toString().contains('Module not found') || 
        e.toString().contains('Import operations require async')) {
      print('✅ $testName - Expected Error: ${e.toString().split('\n')[0]}');
    } else {
      print('❌ $testName - Unexpected Error: $e');
    }
  }
}
