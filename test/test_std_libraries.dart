import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Test Thư viện std cơ bản ===');
  
  testCharLibrary();
  testStringLibrary();
  testArrayLibrary();
  testStringBuilderLibrary();
  testStdDemo();
}

void testCharLibrary() {
  print('\n--- Test 1: char Library ---');
  
  final code = '''
import "std/char.fl" show char, CharType, AsciiConstants;

fun main(): void {
  print("=== Test char cơ bản ===");
  
  // Test tạo char từ ASCII
  let ch_a = char::from_ascii(65); // 'A'
  print("Char A ASCII: " + ch_a.get_ascii_code().to_string());
  
  // Test kiểm tra loại
  print("Is letter: " + ch_a.is_letter().to_string());
  print("Is upper: " + ch_a.is_upper_case().to_string());
  
  // Test chuyển đổi case
  let lower = ch_a.to_lower_case();
  print("Lower ASCII: " + lower.get_ascii_code().to_string());
  
  // Test char từ digit
  let digit = char::from_digit(5);
  print("Digit 5 ASCII: " + digit.get_ascii_code().to_string());
  print("To digit: " + digit.to_digit().to_string());
  
  print("char library test completed");
}

main();
''';

  runTest(code, 'char Library');
}

void testStringLibrary() {
  print('\n--- Test 2: String Library ---');
  
  final code = '''
import "std/string.fl" show String;
import "std/char.fl" show char;

fun main(): void {
  print("=== Test String cơ bản ===");
  
  // Test string rỗng
  let empty = String::new();
  print("Empty length: " + empty.length().to_string());
  
  // Test string từ char
  let ch_h = char::from_ascii(72); // 'H'
  let str_h = String::from_char(ch_h);
  print("String H length: " + str_h.length().to_string());
  
  // Test append char
  let ch_i = char::from_ascii(105); // 'i'
  let hi = str_h.append_char(ch_i);
  print("Hi length: " + hi.length().to_string());
  
  // Test từ số
  let num_str = String::from_number(123);
  print("Number string length: " + num_str.length().to_string());
  
  print("String library test completed");
}

main();
''';

  runTest(code, 'String Library');
}

void testArrayLibrary() {
  print('\n--- Test 3: Array Library ---');
  
  final code = '''
import "std/array.fl" show Array;
import "std/char.fl" show char;

fun main(): void {
  print("=== Test Array cơ bản ===");
  
  // Test array rỗng
  let arr = Array<char>::new();
  print("Empty array length: " + arr.length().to_string());
  
  // Test thêm char
  arr.add(char::from_ascii(65)); // 'A'
  arr.add(char::from_ascii(66)); // 'B'
  print("Array with 2 chars: " + arr.length().to_string());
  
  // Test get
  let first = arr.get(0);
  print("First char ASCII: " + first.get_ascii_code().to_string());
  
  print("Array library test completed");
}

main();
''';

  runTest(code, 'Array Library');
}

void testStringBuilderLibrary() {
  print('\n--- Test 4: StringBuilder Library ---');
  
  final code = '''
import "std/string_builder.fl" show StringBuilder;
import "std/string.fl" show String;
import "std/char.fl" show char;

fun main(): void {
  print("=== Test StringBuilder ===");
  
  // Test builder rỗng
  let builder = StringBuilder::new();
  print("Empty builder length: " + builder.length().to_string());
  
  // Test append string
  let hello = String::from_char(char::from_ascii(72)); // 'H'
  builder.append(hello);
  print("After append H: " + builder.length().to_string());
  
  // Test append char
  builder.append_char(char::from_ascii(105)); // 'i'
  print("After append i: " + builder.length().to_string());
  
  // Test append number
  builder.append_number(123);
  print("After append 123: " + builder.length().to_string());
  
  print("StringBuilder library test completed");
}

main();
''';

  runTest(code, 'StringBuilder Library');
}

void testStdDemo() {
  print('\n--- Test 5: Full std Demo (Parsing Only) ---');
  
  final code = '''
import "std/char.fl" show char, CharType, AsciiConstants;
import "std/string.fl" show String, StringIterator;
import "std/array.fl" show Array, ArrayIterator;
import "std/string_builder.fl" show StringBuilder;
import "std/iterable.fl" show Iterable, Iterator;

fun demo_basic(): void {
    // Test char
    let ch = char::from_ascii(65);
    print("Char ASCII: " + ch.get_ascii_code().to_string());
    
    // Test string
    let str = String::from_char(ch);
    print("String length: " + str.length().to_string());
    
    // Test array
    let arr = Array<char>::new();
    arr.add(ch);
    print("Array length: " + arr.length().to_string());
    
    // Test builder
    let builder = StringBuilder::new();
    builder.append(str);
    print("Builder length: " + builder.length().to_string());
}

fun main(): void {
    print("std Demo parsing test");
    demo_basic();
}

main();
''';

  runTest(code, 'std Demo');
}

void runTest(String code, String testName) {
  try {
    print('Running $testName...');
    
    final parser = Parser();
    final parseResult = parser.parse(code);
    
    if (parseResult.hasError) {
      print('❌ Parse Error in $testName:');
      print('  ${parseResult.error}');
      return;
    }
    
    print('✅ $testName parsed successfully');
    
    // Chỉ test parsing, không chạy vì có thể có dependency issues
    print('   AST nodes: ${parseResult.value.length}');
    
  } catch (e) {
    print('❌ Exception in $testName: $e');
  }
}
